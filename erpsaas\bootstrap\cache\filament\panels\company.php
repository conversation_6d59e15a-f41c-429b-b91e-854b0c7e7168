<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.company.pages.accounting.account-chart' => 'App\\Filament\\Company\\Pages\\Accounting\\AccountChart',
    'app.filament.company.pages.arena-doviz-reports' => 'App\\Filament\\Company\\Pages\\ArenaDovizReports',
    'app.filament.company.pages.create-company' => 'App\\Filament\\Company\\Pages\\CreateCompany',
    'app.filament.company.pages.dashboard' => 'App\\Filament\\Company\\Pages\\Dashboard',
    'app.filament.company.pages.manage-company' => 'App\\Filament\\Company\\Pages\\ManageCompany',
    'app.filament.company.pages.reports' => 'App\\Filament\\Company\\Pages\\Reports',
    'app.filament.company.pages.reports.account-balances' => 'App\\Filament\\Company\\Pages\\Reports\\AccountBalances',
    'app.filament.company.pages.reports.account-transactions' => 'App\\Filament\\Company\\Pages\\Reports\\AccountTransactions',
    'app.filament.company.pages.reports.accounts-payable-aging' => 'App\\Filament\\Company\\Pages\\Reports\\AccountsPayableAging',
    'app.filament.company.pages.reports.accounts-receivable-aging' => 'App\\Filament\\Company\\Pages\\Reports\\AccountsReceivableAging',
    'app.filament.company.pages.reports.balance-sheet' => 'App\\Filament\\Company\\Pages\\Reports\\BalanceSheet',
    'app.filament.company.pages.reports.cash-flow-statement' => 'App\\Filament\\Company\\Pages\\Reports\\CashFlowStatement',
    'app.filament.company.pages.reports.client-balance-summary' => 'App\\Filament\\Company\\Pages\\Reports\\ClientBalanceSummary',
    'app.filament.company.pages.reports.client-payment-performance' => 'App\\Filament\\Company\\Pages\\Reports\\ClientPaymentPerformance',
    'app.filament.company.pages.reports.income-statement' => 'App\\Filament\\Company\\Pages\\Reports\\IncomeStatement',
    'app.filament.company.pages.reports.trial-balance' => 'App\\Filament\\Company\\Pages\\Reports\\TrialBalance',
    'app.filament.company.pages.reports.vendor-balance-summary' => 'App\\Filament\\Company\\Pages\\Reports\\VendorBalanceSummary',
    'app.filament.company.pages.reports.vendor-payment-performance' => 'App\\Filament\\Company\\Pages\\Reports\\VendorPaymentPerformance',
    'app.filament.company.pages.service.connected-account' => 'App\\Filament\\Company\\Pages\\Service\\ConnectedAccount',
    'app.filament.company.pages.service.live-currency' => 'App\\Filament\\Company\\Pages\\Service\\LiveCurrency',
    'app.filament.company.clusters.settings' => 'App\\Filament\\Company\\Clusters\\Settings',
    'app.filament.company.clusters.settings.pages.company-default' => 'App\\Filament\\Company\\Clusters\\Settings\\Pages\\CompanyDefault',
    'app.filament.company.clusters.settings.pages.company-profile' => 'App\\Filament\\Company\\Clusters\\Settings\\Pages\\CompanyProfile',
    'app.filament.company.clusters.settings.pages.localization' => 'App\\Filament\\Company\\Clusters\\Settings\\Pages\\Localization',
    'app.filament.company.clusters.settings.resources.adjustment-resource.pages.create-adjustment' => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\AdjustmentResource\\Pages\\CreateAdjustment',
    'app.filament.company.clusters.settings.resources.adjustment-resource.pages.edit-adjustment' => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\AdjustmentResource\\Pages\\EditAdjustment',
    'app.filament.company.clusters.settings.resources.adjustment-resource.pages.list-adjustments' => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\AdjustmentResource\\Pages\\ListAdjustments',
    'app.filament.company.clusters.settings.resources.currency-resource.pages.create-currency' => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\CurrencyResource\\Pages\\CreateCurrency',
    'app.filament.company.clusters.settings.resources.currency-resource.pages.edit-currency' => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\CurrencyResource\\Pages\\EditCurrency',
    'app.filament.company.clusters.settings.resources.currency-resource.pages.list-currencies' => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\CurrencyResource\\Pages\\ListCurrencies',
    'app.filament.company.clusters.settings.resources.document-default-resource.pages.edit-document-default' => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\DocumentDefaultResource\\Pages\\EditDocumentDefault',
    'app.filament.company.clusters.settings.resources.document-default-resource.pages.list-document-defaults' => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\DocumentDefaultResource\\Pages\\ListDocumentDefaults',
    'app.filament.company.widgets.arena-doviz-overview-widget' => 'App\\Filament\\Company\\Widgets\\ArenaDovizOverviewWidget',
    'app.filament.company.widgets.currency-distribution-widget' => 'App\\Filament\\Company\\Widgets\\CurrencyDistributionWidget',
    'app.filament.company.widgets.top-clients-widget' => 'App\\Filament\\Company\\Widgets\\TopClientsWidget',
    'app.filament.company.widgets.transaction-analytics-widget' => 'App\\Filament\\Company\\Widgets\\TransactionAnalyticsWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'app.filament.pages.auth.login' => 'App\\Filament\\Pages\\Auth\\Login',
    'filament.pages.auth.password-reset.request-password-reset' => 'Filament\\Pages\\Auth\\PasswordReset\\RequestPasswordReset',
    'filament.pages.auth.password-reset.reset-password' => 'Filament\\Pages\\Auth\\PasswordReset\\ResetPassword',
    'wallo.filament-companies.pages.auth.register' => 'Wallo\\FilamentCompanies\\Pages\\Auth\\Register',
    'app.filament.company.resources.currency-exchange-resource.pages.list-currency-exchanges' => 'App\\Filament\\Company\\Resources\\CurrencyExchangeResource\\Pages\\ListCurrencyExchanges',
    'app.filament.company.resources.currency-exchange-resource.pages.create-currency-exchange' => 'App\\Filament\\Company\\Resources\\CurrencyExchangeResource\\Pages\\CreateCurrencyExchange',
    'app.filament.company.resources.currency-exchange-resource.pages.view-currency-exchange' => 'App\\Filament\\Company\\Resources\\CurrencyExchangeResource\\Pages\\ViewCurrencyExchange',
    'app.filament.company.resources.currency-exchange-resource.pages.edit-currency-exchange' => 'App\\Filament\\Company\\Resources\\CurrencyExchangeResource\\Pages\\EditCurrencyExchange',
    'app.filament.company.resources.client-profile-resource.pages.list-client-profiles' => 'App\\Filament\\Company\\Resources\\ClientProfileResource\\Pages\\ListClientProfiles',
    'app.filament.company.resources.client-profile-resource.pages.create-client-profile' => 'App\\Filament\\Company\\Resources\\ClientProfileResource\\Pages\\CreateClientProfile',
    'app.filament.company.resources.client-profile-resource.pages.view-client-profile' => 'App\\Filament\\Company\\Resources\\ClientProfileResource\\Pages\\ViewClientProfile',
    'app.filament.company.resources.client-profile-resource.pages.edit-client-profile' => 'App\\Filament\\Company\\Resources\\ClientProfileResource\\Pages\\EditClientProfile',
    'app.filament.company.resources.arena-doviz.client-balance-resource.pages.list-client-balances' => 'App\\Filament\\Company\\Resources\\ArenaDoviz\\ClientBalanceResource\\Pages\\ListClientBalances',
    'app.filament.company.resources.arena-doviz.client-balance-resource.pages.create-client-balance' => 'App\\Filament\\Company\\Resources\\ArenaDoviz\\ClientBalanceResource\\Pages\\CreateClientBalance',
    'app.filament.company.resources.arena-doviz.client-balance-resource.pages.view-client-balance' => 'App\\Filament\\Company\\Resources\\ArenaDoviz\\ClientBalanceResource\\Pages\\ViewClientBalance',
    'app.filament.company.resources.arena-doviz.client-balance-resource.pages.edit-client-balance' => 'App\\Filament\\Company\\Resources\\ArenaDoviz\\ClientBalanceResource\\Pages\\EditClientBalance',
    'app.filament.company.resources.arena-doviz.delivery-resource.pages.list-deliveries' => 'App\\Filament\\Company\\Resources\\ArenaDoviz\\DeliveryResource\\Pages\\ListDeliveries',
    'app.filament.company.resources.arena-doviz.delivery-resource.pages.create-delivery' => 'App\\Filament\\Company\\Resources\\ArenaDoviz\\DeliveryResource\\Pages\\CreateDelivery',
    'app.filament.company.resources.arena-doviz.delivery-resource.pages.view-delivery' => 'App\\Filament\\Company\\Resources\\ArenaDoviz\\DeliveryResource\\Pages\\ViewDelivery',
    'app.filament.company.resources.arena-doviz.delivery-resource.pages.edit-delivery' => 'App\\Filament\\Company\\Resources\\ArenaDoviz\\DeliveryResource\\Pages\\EditDelivery',
    'app.filament.company.resources.accounting.transaction-resource.pages.list-transactions' => 'App\\Filament\\Company\\Resources\\Accounting\\TransactionResource\\Pages\\ListTransactions',
    'app.filament.company.resources.accounting.transaction-resource.pages.view-transaction' => 'App\\Filament\\Company\\Resources\\Accounting\\TransactionResource\\Pages\\ViewTransaction',
    'app.filament.company.resources.banking.account-resource.pages.list-accounts' => 'App\\Filament\\Company\\Resources\\Banking\\AccountResource\\Pages\\ListAccounts',
    'app.filament.company.resources.banking.account-resource.pages.create-account' => 'App\\Filament\\Company\\Resources\\Banking\\AccountResource\\Pages\\CreateAccount',
    'app.filament.company.resources.banking.account-resource.pages.edit-account' => 'App\\Filament\\Company\\Resources\\Banking\\AccountResource\\Pages\\EditAccount',
  ),
  'clusters' => 
  array (
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Clusters\\Settings.php' => 'App\\Filament\\Company\\Clusters\\Settings',
  ),
  'clusteredComponents' => 
  array (
    'App\\Filament\\Company\\Clusters\\Settings' => 
    array (
      0 => 'App\\Filament\\Company\\Clusters\\Settings\\Pages\\CompanyDefault',
      1 => 'App\\Filament\\Company\\Clusters\\Settings\\Pages\\CompanyProfile',
      2 => 'App\\Filament\\Company\\Clusters\\Settings\\Pages\\Localization',
      3 => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\AdjustmentResource',
      4 => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\CurrencyResource',
      5 => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\DocumentDefaultResource',
    ),
  ),
  'clusterDirectories' => 
  array (
    0 => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament/Company/Clusters',
  ),
  'clusterNamespaces' => 
  array (
    0 => 'App\\Filament\\Company\\Clusters',
  ),
  'pages' => 
  array (
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Accounting\\AccountChart.php' => 'App\\Filament\\Company\\Pages\\Accounting\\AccountChart',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\ArenaDovizReports.php' => 'App\\Filament\\Company\\Pages\\ArenaDovizReports',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Dashboard.php' => 'App\\Filament\\Company\\Pages\\Dashboard',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports.php' => 'App\\Filament\\Company\\Pages\\Reports',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\AccountBalances.php' => 'App\\Filament\\Company\\Pages\\Reports\\AccountBalances',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\AccountTransactions.php' => 'App\\Filament\\Company\\Pages\\Reports\\AccountTransactions',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\AccountsPayableAging.php' => 'App\\Filament\\Company\\Pages\\Reports\\AccountsPayableAging',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\AccountsReceivableAging.php' => 'App\\Filament\\Company\\Pages\\Reports\\AccountsReceivableAging',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\BalanceSheet.php' => 'App\\Filament\\Company\\Pages\\Reports\\BalanceSheet',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\CashFlowStatement.php' => 'App\\Filament\\Company\\Pages\\Reports\\CashFlowStatement',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\ClientBalanceSummary.php' => 'App\\Filament\\Company\\Pages\\Reports\\ClientBalanceSummary',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\ClientPaymentPerformance.php' => 'App\\Filament\\Company\\Pages\\Reports\\ClientPaymentPerformance',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\IncomeStatement.php' => 'App\\Filament\\Company\\Pages\\Reports\\IncomeStatement',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\TrialBalance.php' => 'App\\Filament\\Company\\Pages\\Reports\\TrialBalance',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\VendorBalanceSummary.php' => 'App\\Filament\\Company\\Pages\\Reports\\VendorBalanceSummary',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Reports\\VendorPaymentPerformance.php' => 'App\\Filament\\Company\\Pages\\Reports\\VendorPaymentPerformance',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Service\\ConnectedAccount.php' => 'App\\Filament\\Company\\Pages\\Service\\ConnectedAccount',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Pages\\Service\\LiveCurrency.php' => 'App\\Filament\\Company\\Pages\\Service\\LiveCurrency',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Clusters\\Settings.php' => 'App\\Filament\\Company\\Clusters\\Settings',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Clusters\\Settings\\Pages\\CompanyDefault.php' => 'App\\Filament\\Company\\Clusters\\Settings\\Pages\\CompanyDefault',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Clusters\\Settings\\Pages\\CompanyProfile.php' => 'App\\Filament\\Company\\Clusters\\Settings\\Pages\\CompanyProfile',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Clusters\\Settings\\Pages\\Localization.php' => 'App\\Filament\\Company\\Clusters\\Settings\\Pages\\Localization',
    0 => 'App\\Filament\\Company\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament/Company/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Company\\Pages',
  ),
  'resources' => 
  array (
    0 => 'App\\Filament\\Company\\Resources\\CurrencyExchangeResource',
    1 => 'App\\Filament\\Company\\Resources\\ClientProfileResource',
    2 => 'App\\Filament\\Company\\Resources\\ArenaDoviz\\ClientBalanceResource',
    3 => 'App\\Filament\\Company\\Resources\\ArenaDoviz\\DeliveryResource',
    4 => 'App\\Filament\\Company\\Resources\\Accounting\\TransactionResource',
    5 => 'App\\Filament\\Company\\Resources\\Banking\\AccountResource',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Clusters\\Settings\\Resources\\AdjustmentResource.php' => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\AdjustmentResource',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Clusters\\Settings\\Resources\\CurrencyResource.php' => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\CurrencyResource',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Clusters\\Settings\\Resources\\DocumentDefaultResource.php' => 'App\\Filament\\Company\\Clusters\\Settings\\Resources\\DocumentDefaultResource',
  ),
  'resourceDirectories' => 
  array (
  ),
  'resourceNamespaces' => 
  array (
  ),
  'widgets' => 
  array (
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Widgets\\ArenaDovizOverviewWidget.php' => 'App\\Filament\\Company\\Widgets\\ArenaDovizOverviewWidget',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Widgets\\CurrencyDistributionWidget.php' => 'App\\Filament\\Company\\Widgets\\CurrencyDistributionWidget',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Widgets\\TopClientsWidget.php' => 'App\\Filament\\Company\\Widgets\\TopClientsWidget',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\Company\\Widgets\\TransactionAnalyticsWidget.php' => 'App\\Filament\\Company\\Widgets\\TransactionAnalyticsWidget',
    0 => 'App\\Filament\\Company\\Widgets\\ArenaDovizOverviewWidget',
    1 => 'App\\Filament\\Company\\Widgets\\TransactionAnalyticsWidget',
    2 => 'App\\Filament\\Company\\Widgets\\CurrencyDistributionWidget',
    3 => 'App\\Filament\\Company\\Widgets\\TopClientsWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament/Company/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Company\\Widgets',
  ),
);