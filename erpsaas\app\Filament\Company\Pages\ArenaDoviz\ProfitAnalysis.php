<?php

namespace App\Filament\Company\Pages\ArenaDoviz;

use App\Services\ArenaDoviz\ProfitTrackingService;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\StatsOverviewWidget;
use Illuminate\Contracts\View\View;

class ProfitAnalysis extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static string $view = 'filament.company.pages.arena-doviz.profit-analysis';

    protected static ?string $navigationLabel = 'Profit Analysis';

    protected static ?string $title = 'Buy/Sell Profit Analysis';

    protected static ?string $navigationGroup = 'Arena Doviz';

    protected static ?int $navigationSort = 6;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'start_date' => now()->startOfMonth()->toDateString(),
            'end_date' => now()->endOfMonth()->toDateString(),
            'currency_pair' => 'USD_TO_TRY',
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Analysis Parameters')
                    ->schema([
                        Forms\Components\DatePicker::make('start_date')
                            ->label('Start Date')
                            ->required()
                            ->default(now()->startOfMonth()),

                        Forms\Components\DatePicker::make('end_date')
                            ->label('End Date')
                            ->required()
                            ->default(now()->endOfMonth()),

                        Forms\Components\Select::make('currency_pair')
                            ->label('Currency Pair')
                            ->options([
                                'USD_TO_TRY' => 'USD to TRY',
                                'EUR_TO_TRY' => 'EUR to TRY',
                                'TRY_TO_IRR' => 'TRY to IRR',
                                'USD_TO_IRR' => 'USD to IRR',
                                'EUR_TO_IRR' => 'EUR to IRR',
                                'AED_TO_TRY' => 'AED to TRY',
                            ])
                            ->required()
                            ->default('USD_TO_TRY'),
                    ])
                    ->columns(3),
            ])
            ->statePath('data');
    }

    public function analyze(): void
    {
        $data = $this->form->getState();
        $service = app(ProfitTrackingService::class);
        
        $this->profitAnalysis = $service->calculateProfit(
            $data['currency_pair'],
            $data['start_date'],
            $data['end_date']
        );

        $this->dailyTrends = $service->getDailyProfitTrends(
            $data['start_date'],
            $data['end_date']
        );

        $this->topPairs = $service->getTopCurrencyPairs(
            $data['start_date'],
            $data['end_date']
        );

        $this->locationProfits = $service->getProfitByLocation(
            $data['start_date'],
            $data['end_date']
        );
    }

    public ?array $profitAnalysis = null;
    public ?array $dailyTrends = null;
    public ?array $topPairs = null;
    public ?array $locationProfits = null;

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('analyze')
                ->label('Run Analysis')
                ->icon('heroicon-o-chart-bar')
                ->color('primary')
                ->action('analyze'),

            \Filament\Actions\Action::make('export')
                ->label('Export Report')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->action(function () {
                    // Export functionality can be implemented here
                    $this->notify('success', 'Export functionality will be implemented');
                }),
        ];
    }

    protected function getViewData(): array
    {
        return [
            'profitAnalysis' => $this->profitAnalysis,
            'dailyTrends' => $this->dailyTrends,
            'topPairs' => $this->topPairs,
            'locationProfits' => $this->locationProfits,
        ];
    }

    public function getStatsOverview(): array
    {
        if (!$this->profitAnalysis) {
            return [];
        }

        $analysis = $this->profitAnalysis['profit_analysis'];
        
        return [
            StatsOverviewWidget\Stat::make('Rate Spread', $analysis['formatted_spread'])
                ->description('Average buy vs sell rate difference')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color($analysis['rate_spread'] > 0 ? 'success' : 'danger'),

            StatsOverviewWidget\Stat::make('Matched Volume', number_format($analysis['matched_volume'], 2))
                ->description('Volume with both buy and sell transactions')
                ->descriptionIcon('heroicon-m-scale')
                ->color('info'),

            StatsOverviewWidget\Stat::make('Estimated Profit', $analysis['formatted_profit'])
                ->description('Theoretical profit + commissions')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),

            StatsOverviewWidget\Stat::make('Rate Spread %', number_format($analysis['rate_spread_percent'], 2) . '%')
                ->description('Percentage spread on rates')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($analysis['rate_spread_percent'] > 1 ? 'success' : 'warning'),
        ];
    }

    public function getBuyTransactionsTable(): Table
    {
        if (!$this->profitAnalysis) {
            return Table::make();
        }

        $summary = $this->profitAnalysis['buy_summary'];
        
        return Table::make()
            ->heading('Buy Transactions Summary')
            ->columns([
                Tables\Columns\TextColumn::make('metric')
                    ->label('Metric'),
                Tables\Columns\TextColumn::make('value')
                    ->label('Value')
                    ->alignment(Alignment::End),
            ])
            ->records([
                ['metric' => 'Transaction Count', 'value' => number_format($summary['transaction_count'])],
                ['metric' => 'Total Amount', 'value' => $summary['formatted_total_amount']],
                ['metric' => 'Total Value', 'value' => $summary['formatted_total_value']],
                ['metric' => 'Total Commission', 'value' => $summary['formatted_commission']],
                ['metric' => 'Average Rate', 'value' => $summary['formatted_avg_rate']],
            ]);
    }

    public function getSellTransactionsTable(): Table
    {
        if (!$this->profitAnalysis) {
            return Table::make();
        }

        $summary = $this->profitAnalysis['sell_summary'];
        
        return Table::make()
            ->heading('Sell Transactions Summary')
            ->columns([
                Tables\Columns\TextColumn::make('metric')
                    ->label('Metric'),
                Tables\Columns\TextColumn::make('value')
                    ->label('Value')
                    ->alignment(Alignment::End),
            ])
            ->records([
                ['metric' => 'Transaction Count', 'value' => number_format($summary['transaction_count'])],
                ['metric' => 'Total Amount', 'value' => $summary['formatted_total_amount']],
                ['metric' => 'Total Value', 'value' => $summary['formatted_total_value']],
                ['metric' => 'Total Commission', 'value' => $summary['formatted_commission']],
                ['metric' => 'Average Rate', 'value' => $summary['formatted_avg_rate']],
            ]);
    }

    public function getRecommendationsData(): array
    {
        if (!$this->profitAnalysis || empty($this->profitAnalysis['recommendations'])) {
            return [];
        }

        return $this->profitAnalysis['recommendations'];
    }

    public function getTopCurrencyPairsData(): array
    {
        return $this->topPairs ?: [];
    }

    public function getDailyTrendsData(): array
    {
        return $this->dailyTrends ?: [];
    }
}
