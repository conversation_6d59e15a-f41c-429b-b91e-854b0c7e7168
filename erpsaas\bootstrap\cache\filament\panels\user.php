<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.user.clusters.account' => 'App\\Filament\\User\\Clusters\\Account',
    'app.filament.user.clusters.account.pages.personal-access-tokens' => 'App\\Filament\\User\\Clusters\\Account\\Pages\\PersonalAccessTokens',
    'app.filament.user.clusters.account.pages.profile' => 'App\\Filament\\User\\Clusters\\Account\\Pages\\Profile',
    'wallo.filament-companies.pages.user.profile' => 'Wallo\\FilamentCompanies\\Pages\\User\\Profile',
    'wallo.filament-companies.pages.user.personal-access-tokens' => 'Wallo\\FilamentCompanies\\Pages\\User\\PersonalAccessTokens',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
  ),
  'clusters' => 
  array (
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\User\\Clusters\\Account.php' => 'App\\Filament\\User\\Clusters\\Account',
  ),
  'clusteredComponents' => 
  array (
    'App\\Filament\\User\\Clusters\\Account' => 
    array (
      0 => 'App\\Filament\\User\\Clusters\\Account\\Pages\\PersonalAccessTokens',
      1 => 'App\\Filament\\User\\Clusters\\Account\\Pages\\Profile',
    ),
  ),
  'clusterDirectories' => 
  array (
    0 => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament/User/Clusters',
  ),
  'clusterNamespaces' => 
  array (
    0 => 'App\\Filament\\User\\Clusters',
  ),
  'pages' => 
  array (
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\User\\Clusters\\Account.php' => 'App\\Filament\\User\\Clusters\\Account',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\User\\Clusters\\Account\\Pages\\PersonalAccessTokens.php' => 'App\\Filament\\User\\Clusters\\Account\\Pages\\PersonalAccessTokens',
    'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament\\User\\Clusters\\Account\\Pages\\Profile.php' => 'App\\Filament\\User\\Clusters\\Account\\Pages\\Profile',
    0 => 'Wallo\\FilamentCompanies\\Pages\\User\\Profile',
    1 => 'Wallo\\FilamentCompanies\\Pages\\User\\PersonalAccessTokens',
  ),
  'pageDirectories' => 
  array (
    0 => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament/User/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\User\\Pages',
  ),
  'resources' => 
  array (
  ),
  'resourceDirectories' => 
  array (
    0 => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament/User/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\User\\Resources',
  ),
  'widgets' => 
  array (
  ),
  'widgetDirectories' => 
  array (
    0 => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\app\\Filament/User/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\User\\Widgets',
  ),
);